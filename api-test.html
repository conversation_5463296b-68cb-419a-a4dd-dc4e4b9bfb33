<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.5;
        }
        button {
            padding: 8px 16px;
            background-color: #0070f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0051a8;
        }
        pre {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-word;
        }
        .card {
            border: 1px solid #eaeaea;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .card h2 {
            margin-top: 0;
        }
        .log {
            height: 200px;
            overflow-y: auto;
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin-top: 10px;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>API Test</h1>
    <p>This page tests the model usage API directly from the public folder.</p>
    
    <div class="card">
        <h2>Send Test Data</h2>
        <button onclick="sendTestData()">Send Random Data</button>
        <button onclick="fetchData()">Fetch Data</button>
        <button onclick="clearData()">Clear All Data</button>
    </div>
    
    <div class="card">
        <h2>Response</h2>
        <pre id="response">No data yet</pre>
    </div>
    
    <div class="card">
        <h2>Log</h2>
        <div id="log" class="log"></div>
    </div>
    
    <script>
        // Log messages
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logElement.prepend(entry);
        }
        
        // Update response display
        function updateResponse(data) {
            document.getElementById('response').textContent = 
                typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }
        
        // Send test data to API
        async function sendTestData() {
            log('Sending test data...', 'info');
            updateResponse('Sending request...');
            
            try {
                // Create test data
                const testData = {
                    modelName: 'Test Model ' + Math.floor(Math.random() * 100),
                    imageCount: Math.floor(Math.random() * 50) + 1
                };
                
                log(`Data: ${JSON.stringify(testData)}`, 'info');
                
                // Send data directly to the API endpoint
                const response = await fetch('/api/model-usage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                // Get response as text first (for debugging)
                const responseText = await response.text();
                
                try {
                    // Try to parse as JSON
                    const result = JSON.parse(responseText);
                    updateResponse(result);
                    
                    if (result.success) {
                        log(`Data sent successfully: ${testData.modelName}, ${testData.imageCount} images`, 'success');
                    } else {
                        log(`API Error: ${result.error || 'Unknown error'}`, 'error');
                    }
                } catch (parseError) {
                    // If not valid JSON, show the raw response
                    updateResponse(responseText);
                    log(`Error parsing response: ${parseError.message}`, 'error');
                }
            } catch (error) {
                updateResponse(`Error: ${error.message}`);
                log(`Network Error: ${error.message}`, 'error');
            }
        }
        
        // Fetch data from API
        async function fetchData() {
            log('Fetching data...', 'info');
            updateResponse('Sending request...');
            
            try {
                // Fetch data directly from the API endpoint
                const response = await fetch('/api/model-usage');
                
                log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                // Get response as text first (for debugging)
                const responseText = await response.text();
                
                try {
                    // Try to parse as JSON
                    const result = JSON.parse(responseText);
                    updateResponse(result);
                    
                    if (result.success) {
                        log(`Data fetched successfully: ${result.data?.length || 0} items`, 'success');
                    } else {
                        log(`API Error: ${result.error || 'Unknown error'}`, 'error');
                    }
                } catch (parseError) {
                    // If not valid JSON, show the raw response
                    updateResponse(responseText);
                    log(`Error parsing response: ${parseError.message}`, 'error');
                }
            } catch (error) {
                updateResponse(`Error: ${error.message}`);
                log(`Network Error: ${error.message}`, 'error');
            }
        }
        
        // Clear all data
        async function clearData() {
            log('Clearing data...', 'info');
            updateResponse('Sending request...');
            
            try {
                // Send DELETE request to the API endpoint
                const response = await fetch('/api/model-usage', {
                    method: 'DELETE'
                });
                
                log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                // Get response as text first (for debugging)
                const responseText = await response.text();
                
                try {
                    // Try to parse as JSON
                    const result = JSON.parse(responseText);
                    updateResponse(result);
                    
                    if (result.success) {
                        log('All data cleared successfully', 'success');
                    } else {
                        log(`API Error: ${result.error || 'Unknown error'}`, 'error');
                    }
                } catch (parseError) {
                    // If not valid JSON, show the raw response
                    updateResponse(responseText);
                    log(`Error parsing response: ${parseError.message}`, 'error');
                }
            } catch (error) {
                updateResponse(`Error: ${error.message}`);
                log(`Network Error: ${error.message}`, 'error');
            }
        }
        
        // Log initial message
        log('API test page loaded', 'info');
    </script>
</body>
</html>
